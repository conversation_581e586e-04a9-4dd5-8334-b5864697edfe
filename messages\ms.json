{"common": {"loading": "Memuat...", "save": "Simpan", "cancel": "<PERSON><PERSON>", "submit": "Hantar", "edit": "Edit", "delete": "Hapus", "confirm": "<PERSON><PERSON><PERSON>", "back": "Kembali", "next": "Seterusnya", "previous": "Sebelumnya", "search": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "notSet": "Tidak ditetapkan", "unknown": "Tidak diketahui", "firstTime": "<PERSON> pertama", "justNow": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>"}, "invitation": {"loading": "Memuatkan jemputan...", "title": "Anda <PERSON>!", "description": "{inviterName} telah menje<PERSON> anda untuk menyertai projek", "someone": "Seseorang", "projectDetails": {"title": "Butiran <PERSON>", "project": "Projek", "role": "<PERSON><PERSON><PERSON>", "invitedBy": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>h", "unknownProject": "Projek Tidak Diketahui", "member": "<PERSON><PERSON>", "unknown": "Tidak Diketahui"}, "nextStep": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>a akan diminta untuk mencipta kata laluan untuk menyediakan akaun SimPLE anda."}, "button": {"accepting": "Meneri<PERSON>...", "accept": "<PERSON><PERSON>", "acceptAndSetup": "Terima & Sediakan <PERSON>un"}, "disclaimer": "<PERSON>gan menerima jem<PERSON>an ini, anda bersetuju untuk menyertai projek ini dan bekerjasama dengan ahli pasukan.", "invalid": {"title": "Jemputan Tidak Sah", "description": "Pautan jemputan ini tidak sah, telah tamat tempoh, atau telah digunakan. Sila hubungi orang yang menjemput anda untuk jemputan baru."}, "error": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> berlaku semasa memu<PERSON> jemputan. <PERSON>la cuba lagi atau hubungi sokongan."}, "accepted": {"title": "Jemputan <PERSON>!", "description": "<PERSON>a telah berjaya <PERSON> {projectName}. Mengalihkan ke papan pemuka projek...", "unknownProject": "projek tersebut"}, "redirecting": "Mengalihkan ke projek...", "accept": {"title": "Jemputan <PERSON>", "description": "Anda telah dijemput untuk menyertai projek", "success": "<PERSON><PERSON><PERSON><PERSON> {projectName}!", "error": "<PERSON><PERSON> j<PERSON>: {error}", "expired": "Jemputan ini telah tamat tempoh atau tidak sah", "loading": "Memproses jemputan...", "acceptButton": "<PERSON><PERSON>", "loginRequired": "Sila log masuk untuk menerima jemputan ini"}}, "navigation": {"navigation": "Na<PERSON><PERSON><PERSON>", "professionalEdition": "Portal Kontraktor", "signingOut": "Sedang log keluar...", "signOut": "<PERSON><PERSON>", "loadingMenu": "Memuatkan menu...", "languages": {"en": "English", "ms": "Bahasa Malaysia"}, "project": "Projek", "projects": "Projek", "lifts": "Lif", "buildings": "Bangunan", "directory": "Direktori CP", "dailyLogs": "<PERSON><PERSON>", "pmas": "Pengurusan PMA", "complaints": "<PERSON><PERSON>", "contractors": "Kontraktor", "clients": "<PERSON><PERSON><PERSON>", "blacklist": "<PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "users": "Pengguna", "profile": "Profil", "settings": "Tetapan", "maintenanceLogs": "<PERSON><PERSON>", "members": "<PERSON><PERSON>", "login": "Log Ma<PERSON>k", "register": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>", "descriptions": {"dashboard": "<PERSON><PERSON><PERSON>n k<PERSON>lu<PERSON>han aktiviti pengurusan lif", "projects": "<PERSON><PERSON> projek dan tugas yang diberikan kepada anda", "lifts": "Urus inventori dan status lif", "buildings": "<PERSON><PERSON> mak<PERSON> bang<PERSON>n", "contracts": "Pengurusan kontrak <PERSON>len<PERSON>an", "dailyLogs": "Log pemantauan dan penyelengg<PERSON>an harian", "pmas": "<PERSON><PERSON><PERSON><PERSON>", "complaints": "<PERSON><PERSON> dan isu pelanggan", "contractors": "<PERSON><PERSON> pendaftaran dan pensijilan kontraktor", "clients": "<PERSON>rus maklumat klien", "blacklist": "Pen<PERSON><PERSON>an kontraktor senarai hitam", "analytics": "<PERSON><PERSON><PERSON> prestasi dan pandangan", "reports": "Jana dan lihat laporan sistem", "users": "Pengurusan pengguna sistem", "profile": "<PERSON>rus tetapan profil anda", "settings": "Tetapan k<PERSON>figu<PERSON>i sistem"}}, "pmaManagement": {"title": "Tambah Butiran PMA", "subtitle": "Ma<PERSON><PERSON><PERSON> butiran untuk setiap tugasan PMA", "newLogEntry": "<PERSON><PERSON>", "searchPlaceholder": "Cari log PMA...", "filter": "<PERSON><PERSON>", "export": "Eksport", "addMorePmaEntries": "Tambah lebih banyak entri PMA di atas", "form": {"pmaInformation": "Maklumat PMA", "pmaInformationDescription": "Lengkapkan semua medan untuk menambah tugasan PMA baru", "addedPmaEntries": "Entri PMA Ditambah", "addedPmaEntriesDescription": "Semak entri PMA anda sebelum penyer<PERSON>", "customer": "Pelanggan", "customerPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama pelanggan", "agency": "Agensi", "agencyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama agensi", "location": "<PERSON><PERSON>", "locationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> butiran lokasi", "pmaNumber": "No. PMA", "pmaNumberPlaceholder": "Masukkan nombor PMA", "type": "Jenama", "supervisor": "Pegawai <PERSON>", "supervisorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penyelia", "dateReceived": "<PERSON><PERSON><PERSON>", "assignCp": "Tugaskan CP", "ready": "Sedia", "edit": "Edit", "delete": "Padam"}, "actions": {"saveDraft": "Simpan Dr<PERSON>", "addAnotherPma": "Tambah PMA Lain", "cancel": "<PERSON><PERSON>", "submitPma": "Hantar PMA"}, "statistics": {"activePmas": "PMA Aktif", "completed": "Se<PERSON><PERSON>", "expiringSoon": "<PERSON>kan <PERSON> Tempoh", "overdue": "Lewat <PERSON>mpoh"}, "activityLogs": {"title": "Log Aktiviti PMA", "subtitle": "<PERSON><PERSON> dan urus semua entri PMA dan status semasa mereka"}, "table": {"pmaNumber": "Nombor PMA", "customer": "Pelanggan", "project": "Projek", "contractor": "Kontraktor", "createdDate": "<PERSON><PERSON><PERSON>", "expiryDate": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Aktif", "expiringSoon": "<PERSON>kan <PERSON> Tempoh", "expired": "Tamat Tempoh", "completed": "Se<PERSON><PERSON>"}, "pagination": {"showing": "<PERSON><PERSON><PERSON><PERSON><PERSON> {count} da<PERSON><PERSON> {total} entri", "previous": "Sebelumnya", "next": "Seterusnya"}}, "dashboard": {"title": "<PERSON><PERSON>", "welcomeBack": "<PERSON><PERSON><PERSON> kembali, {name}!", "errorLoading": "<PERSON><PERSON> me<PERSON> profil: {error}", "profile": {"title": "<PERSON><PERSON>", "email": "E-mel", "name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "phone": "Telefon", "memberSince": "<PERSON><PERSON>", "lastLogin": "Log Ma<PERSON>"}, "quickActions": {"title": "<PERSON><PERSON><PERSON>", "editProfile": "Edit Profil", "settings": "Tetapan"}, "recentActivity": {"title": "Aktiviti Terkini", "noActivity": "Tiada aktiviti terkini untuk dipaparkan."}, "statistics": {"title": "Statistik Akaun", "loginStatus": "Status Log Masuk", "accountType": "<PERSON><PERSON>", "lastLogin": "Log Ma<PERSON>", "active": "Aktif", "standard": "Standard"}}, "pages": {"projects": {"title": "Projek", "description": "<PERSON><PERSON> projek dan tugas yang diberikan kepada anda", "noProjects": "Tiada Projek <PERSON>", "noProjectsDescription": "Anda belum mempunyai projek yang diberikan lagi.", "createFirst": "Cipta Projek Pertama Anda", "stats": {"total": "<PERSON><PERSON><PERSON>", "active": "Aktif", "pending": "Tertangguh", "completed": "Se<PERSON><PERSON>"}, "create": {"title": "Cipta Projek Baharu", "description": "<PERSON><PERSON> mak<PERSON> di bawah untuk mencipta projek baharu", "form": {"title": "Maklumat Projek", "description": "<PERSON><PERSON><PERSON><PERSON> maklumat asas tentang projek anda", "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama projek"}, "code": {"label": "Nombor sebut harga", "placeholder": "cth., QUO-2025-001"}, "location": {"label": "<PERSON><PERSON>", "placeholder": "Masukkan lokasi projek"}, "startDate": {"label": "<PERSON><PERSON><PERSON>"}, "endDate": {"label": "<PERSON><PERSON><PERSON>"}, "status": {"label": "Status", "placeholder": "Pilih status projek", "options": {"pending": "Tertangguh", "active": "Aktif", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>"}}, "description": {"label": "Penerangan", "placeholder": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>gan projek (pilihan)"}}, "actions": {"create": "Cipta <PERSON>", "creating": "Mencipta..."}}, "validation": {"required": "Medan yang diperlukan hilang", "requiredFields": "<PERSON>la isi semua medan yang diperlukan", "dateError": "Julat tarikh tidak sah", "endDateAfterStart": "<PERSON><PERSON>h tamat mesti selepas tarikh mula"}, "success": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> dicipta", "description": "<PERSON><PERSON><PERSON> baharu anda telah dicipta dan sedia untuk digunakan"}, "error": {"title": "<PERSON><PERSON> mencipta projek", "description": "Terdapat ralat semasa mencipta projek. Sila cuba lagi"}}}, "members": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> ahli pasukan projek dan peranan mereka", "addMember": "<PERSON><PERSON> Ahli", "teamMembers": "<PERSON><PERSON>", "noMembers": "<PERSON><PERSON><PERSON> ahli pasukan diju<PERSON>ai", "noMembersDescription": "Projek ini belum mempunyai ahli pasukan lagi.", "addFirstMember": "<PERSON><PERSON> ahli pasukan pertama anda", "modal": {"title": "<PERSON><PERSON> Ah<PERSON> Baharu", "description": "<PERSON><PERSON><PERSON> ahli baharu untuk menyertai projek ini", "addAnother": "<PERSON><PERSON> ahli lain", "removeMember": "<PERSON><PERSON>", "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat emel ahli"}, "role": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>n", "options": {"technician": "Juruteknik"}}, "errors": {"alreadyAdded": "Pengguna ini sudah menjadi ahli projek ini", "userNotFound": "Pengguna dengan emel ini tidak wujud dalam sistem", "invalidEmail": "<PERSON><PERSON> masukkan alamat emel yang sah", "duplicateEmail": "Emel ini telah dimasukkan", "addingFailed": "<PERSON><PERSON> menambah ahli ini ke projek", "inviteFailed": "<PERSON><PERSON> men<PERSON> jemputan kepada {email}: {error}"}, "status": {"success": "<PERSON><PERSON><PERSON><PERSON>", "alreadyAdded": "<PERSON><PERSON> men<PERSON> ahli", "userNotFound": "Pengguna tidak dijumpai", "invited": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>"}, "success": {"invited": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON> kepada {email}"}, "actions": {"cancel": "<PERSON><PERSON>", "add": "<PERSON><PERSON> Ahli", "adding": "Menambah..."}}}}, "auth": {"loginTitle": "Log masuk ke akaun anda", "loginSubtitle": "<PERSON><PERSON><PERSON><PERSON> emel anda di bawah untuk log masuk ke akaun anda", "registerTitle": "<PERSON><PERSON><PERSON> anda", "registerSubtitle": "<PERSON><PERSON><PERSON><PERSON> mak<PERSON>at anda di bawah untuk mencipta akaun anda", "email": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>", "fullName": "<PERSON><PERSON>", "phoneNumber": "Nombor Telefon", "role": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "fullNamePlaceholder": "<PERSON>", "phonePlaceholder": "+*********** atau **********", "forgotPassword": "Lupa kata laluan anda?", "selectRole": "<PERSON><PERSON><PERSON> peranan anda", "jkr": "JKR (Jabatan Kerja Raya)", "jkrPic": "JKR PIC (Pegawai Bertanggungjawab)", "jkrAdmin": "JKR Admin", "admin": "Pentadbir", "contractor": "Kontraktor", "viewer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "login": "Log Ma<PERSON>k", "signUp": "<PERSON><PERSON><PERSON>", "signIn": "Log masuk", "createAccount": "<PERSON><PERSON><PERSON>", "signingIn": "Sedang log masuk...", "creatingAccount": "Sedang mencipta akaun...", "noAccount": "Tidak mempunyai akaun?", "alreadyHaveAccount": "Sudah mempunyai akaun?", "phoneTooltip1": "Nombor telefon Malaysia sahaja", "phoneTooltip2": "cth., +***********, **********", "passwordRequirements": "<PERSON>a la<PERSON>an mesti mengandu<PERSON>i:", "passwordReq1": "Sekurang-kurangnya 8 aksara", "passwordReq2": "<PERSON><PERSON> (A-Z)", "passwordReq3": "<PERSON><PERSON> huruf kecil (a-z)", "passwordReq4": "Satu <PERSON><PERSON> (0-9)", "passwordReq5": "<PERSON>tu aksara khas (@$!%*?&)", "loginSuccess": "<PERSON><PERSON><PERSON><PERSON> log masuk! Mengalihkan ke papan pemuka...", "loginFailed": "Log masuk gagal. Sila cuba lagi.", "registerSuccess": "Akaun berjaya dicipta! Sila semak emel anda untuk mengesahkan akaun anda.", "registerFailed": "<PERSON><PERSON> mencipta akaun. Sila cuba lagi.", "userExists": "<PERSON><PERSON>un dengan emel ini sudah wujud. Sila cuba log masuk sebaliknya.", "adminLoginTitle": "Log masuk sebagai Pentadbir", "adminLoginSubtitle": "<PERSON><PERSON><PERSON><PERSON> butiran pentad<PERSON> anda untuk log masuk", "adminRegisterTitle": "Daftar sebagai Pentadbir", "adminRegisterSubtitle": "<PERSON><PERSON><PERSON> akaun pentad<PERSON> baharu", "contractorLoginTitle": "Log masuk sebagai Kontraktor", "contractorLoginSubtitle": "<PERSON><PERSON><PERSON><PERSON> butiran kontraktor anda untuk log masuk", "contractorRegisterTitle": "Daftar sebagai Kontraktor", "contractorRegisterSubtitle": "<PERSON><PERSON><PERSON> akaun k<PERSON> baharu", "adminLogin": "Log Masuk <PERSON>bir", "contractorLogin": "Log Masuk <PERSON>", "adminSignUp": "<PERSON><PERSON><PERSON>", "adminSignIn": "Log masuk <PERSON>", "contractorSignUp": "<PERSON><PERSON>ar Kontraktor", "contractorSignIn": "Log masuk <PERSON>", "createAdminAccount": "<PERSON><PERSON><PERSON>", "createContractorAccount": "<PERSON><PERSON><PERSON>", "creatingAdminAccount": "Mencipta <PERSON>...", "creatingContractorAccount": "<PERSON>ci<PERSON>...", "noAdminAccount": "<PERSON><PERSON><PERSON> akaun pentadbir?", "noContractorAccount": "<PERSON><PERSON><PERSON> akaun kontraktor?", "alreadyHaveAdminAccount": "Sudah mempunyai akaun pentadbir?", "alreadyHaveContractorAccount": "Sudah mempunyai akaun kontraktor?", "adminLoginSuccess": "<PERSON><PERSON><PERSON><PERSON> log masuk sebagai pentadbir!", "contractorLoginSuccess": "<PERSON><PERSON><PERSON><PERSON> log masuk sebagai kontraktor!", "adminRegisterSuccess": "<PERSON><PERSON><PERSON> pentadbir berjaya dicipta!", "contractorRegisterSuccess": "<PERSON><PERSON><PERSON> kontraktor berjaya dicipta!", "contractorNote": "Nota: <PERSON><PERSON><PERSON><PERSON> syarikat tambahan akan diperlukan semasa pen<PERSON>.", "contractorNoteSubtext": "<PERSON><PERSON> akan <PERSON><PERSON>n but<PERSON>n <PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan keupa<PERSON>an projek selepas pendaft<PERSON>.", "profileCreationError": "Pendaftaran gagal semasa penciptaan profil. Sila cuba lagi atau hubungi sokongan.", "signupDisabled": "Pendaftaran akaun kini dilumpuhkan. Sila hubungi sokongan.", "forgotPasswordTitle": "Tetapkan semula kata laluan anda", "forgotPasswordSubtitle": "<PERSON><PERSON><PERSON><PERSON> emel anda di bawah dan kami akan hantar pautan tetapan semula", "sendResetLink": "Hantar Pa<PERSON>", "sendingResetLink": "Menghan<PERSON> pautan tetapan semula...", "resetLinkSent": "<PERSON><PERSON>k emel anda untuk pautan tetapan semula", "resetLinkFailed": "<PERSON>l menghantar pautan tetapan semula. Sila cuba lagi.", "backToLogin": "Ke<PERSON>li ke log masuk", "verifyCodeTitle": "<PERSON>sukka<PERSON> kod pengesahan", "verifyCodeSubtitle": "Masukkan kod 6-digit yang dihantar ke emel anda", "verificationCode": "<PERSON><PERSON>", "codePlaceholder": "Masukkan kod 6-digit", "verifyCode": "<PERSON><PERSON><PERSON>", "verifyingCode": "Mengesahkan kod...", "codeVerified": "Kod berjaya disahkan! Mengalihkan...", "codeVerificationFailed": "Kod tidak sah atau telah tamat tempoh. Sila cuba lagi.", "didNotReceiveCode": "Tidak menerima kod?", "resendCode": "<PERSON><PERSON> semula kod", "alreadyHaveCode": "Sudah mempunyai kod pengesahan?", "resetPasswordTitle": "Tetapkan kata laluan baru anda", "resetPasswordSubtitle": "<PERSON><PERSON>h kata laluan yang kuat untuk melindungi akaun anda", "newPassword": "<PERSON><PERSON>", "confirmNewPassword": "<PERSON><PERSON><PERSON>", "newPasswordPlaceholder": "<PERSON><PERSON><PERSON>n kata laluan baru anda", "confirmPasswordPlaceholder": "<PERSON>hkan kata laluan baru anda", "updatePassword": "<PERSON><PERSON>", "updatingPassword": "Mengemas kini kata laluan...", "passwordUpdated": "Kata laluan berjaya dikemas kini! Sila log masuk dengan kata laluan baru anda.", "passwordUpdateFailed": "Gagal mengemas kini kata laluan. Sila cuba lagi."}, "contractor": {"onboarding": {"title": "Penyiapan Kontraktor", "step1": {"title": "Maklumat Peribadi", "fullName": "<PERSON><PERSON>", "fullNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penuh anda", "icNumber": "Nombor IC", "icNumberPlaceholder": "123456-78-9012", "phoneNumber": "Nombor Telefon", "phoneNumberPlaceholder": "01X-XXXXXXX", "role": "<PERSON><PERSON><PERSON>", "rolePlaceholder": "<PERSON><PERSON><PERSON> peranan anda", "roleHelp": "<PERSON><PERSON><PERSON> peranan anda dalam syarikat", "technician": "Juruteknik", "admin": "Pentadbir", "cp": "Orang Kompeten", "nextButton": "Seterusnya: <PERSON><PERSON><PERSON>"}, "step2": {"title": "<PERSON><PERSON><PERSON><PERSON> - {role}", "nextButton": "Seterusnya: <PERSON><PERSON><PERSON><PERSON>", "cp": {"title": "Maklumat Orang Kompeten", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON>kkan nama orang kompeten", "category": "<PERSON><PERSON><PERSON>", "categoryPlaceholder": "<PERSON><PERSON><PERSON> ka<PERSON>gori <PERSON>", "categoryA": "Kategori A", "categoryB": "Kategori B", "categoryC": "Kategori C", "icNo": "Nombor IC", "icNoPlaceholder": "123456-78-9012", "cpNumber": "Nombor CP", "cpNumberPlaceholder": "Masukkan nombor pendaftaran CP", "tel": "Telefon", "telPlaceholder": "01X-XXXXXXX", "email": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "liftListFiles": "<PERSON><PERSON><PERSON>", "liftFilesPlateholder": "Muat naik fail senarai lif (PDF, DOC, DOCX, XLS, XLSX)", "noLiftFiles": "Tiada fail senarai lif dimuat naik lagi", "registrationCertFile": "<PERSON><PERSON>", "registrationCertFilePlaceholder": "<PERSON>at naik sijil pendaft<PERSON> (PDF, DOC, DOCX, JPG, PNG)", "registrationCertFileHelp": "Muat naik fail sijil pendaftaran CP anda (minimum 10MB)"}, "admin": {"title": "Maklumat Pentadbir", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama pentadbir", "nameHelp": "Ini akan digunakan untuk pengenalan pentadbiran"}, "technician": {"title": "Maklumat Juruteknik", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama juruteknik", "nameHelp": "Ini akan digunakan untuk pengenalan juruteknik"}}, "step3": {"title": "<PERSON><PERSON><PERSON><PERSON>", "registrationType": "<PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON>", "createDescription": "Daftarkan syarikat baru yang belum didaftarkan dalam sistem", "joinTitle": "Sertai Syarikat Sedia Ada", "joinDescription": "Sertai syarikat yang sudah didaftarkan dalam sistem", "specialCode": "<PERSON><PERSON>", "specialCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> kod khas yang diberikan oleh syarikat", "specialCodeHelp": "<PERSON><PERSON><PERSON><PERSON> kod khas yang diberikan oleh pentadbir syarikat anda", "continueToCreation": "Teruskan ke Penciptaan <PERSON>", "joinCompanyButton": "Sertai <PERSON>ika<PERSON>"}, "step4": {"title": "<PERSON><PERSON><PERSON><PERSON>", "companyCode": "<PERSON><PERSON>", "companyCodeHelp": "Kod unik ini akan mengenal pasti syarikat anda dalam sistem", "companyName": "<PERSON><PERSON>", "companyNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama syarikat anda", "companyNameHelp": "<PERSON><PERSON> s<PERSON><PERSON>t akan ditukar kepada huruf besar dan mesti unik.", "companyNamePreview": "Pratonton: {name}", "companyType": "<PERSON><PERSON>", "companyTypeHelp": "<PERSON><PERSON><PERSON> jeni<PERSON> s<PERSON> anda", "companyHotline": "<PERSON><PERSON>", "companyHotlinePlaceholder": "Masukkan nombor talian k<PERSON><PERSON><PERSON> syar<PERSON>t", "oemName": "<PERSON>a OEM", "oemNamePlaceholder": "Ma<PERSON>kkan nama OEM", "oemNameHelp": "<PERSON><PERSON><PERSON><PERSON> untuk syarikat jenis O<PERSON>", "appointedOemCompetentFirm": "OEM/Firma Berkebolehan Dilantik", "appointedOemCompetentFirmPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama OEM atau firma berkebolehan yang dilantik", "appointedOemCompetentFirmHelp": "Diperlukan untuk firma tidak berkebolehan - nyatakan OEM atau firma berkebolehan yang dilantik untuk perkhidmatan anda", "createButton": "<PERSON><PERSON><PERSON>", "creatingButton": "<PERSON><PERSON><PERSON>..."}, "progress": {"step": "Langka<PERSON> {current} daripada {total}", "personalInfo": "Maklumat Peribadi", "companyInfo": "Mak<PERSON>at Syarikat", "finalDetails": "<PERSON><PERSON><PERSON>"}}}, "company": {"types": {"sdn_bhd": "Sdn Bhd", "bhd": "Bhd", "partnership": "<PERSON>kon<PERSON><PERSON>", "sole_proprietorship": "<PERSON><PERSON><PERSON>", "llp": "LLP", "competent_firm": "<PERSON><PERSON>", "non_competent_firm": "<PERSON>rma Tidak <PERSON>", "oem": "OEM (Pengilang Peralatan Asal)"}, "availability": {"checking": "Memeriksa ketersediaan...", "available": "<PERSON><PERSON> s<PERSON>ikat tersedia", "unavailable": "<PERSON>a syarikat sudah digunakan", "error": "<PERSON><PERSON> me<PERSON> k<PERSON>"}}, "agencies": {"JKR": "Jabatan Kerja Raya Malaysia (JKR)", "KKM": "Kementerian Kesihatan Malaysia (KKM)", "KPM": "Kementerian Pendidikan Malaysia (KPM)", "KPKT": "Kementerian Perumahan dan <PERSON>mpatan (KPKT)", "KKR": "Kementerian <PERSON> (KKdW)", "KPDNHEP": "Kementerian Perdagangan Dalam Nege<PERSON> dan <PERSON> (KPDNHEP)", "MOSTI": "Kementerian Sains, Teknologi <PERSON> (MOSTI)", "KPWKM": "Kementerian Pembangunan <PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (KPWKM)", "DBKL": "Dewan Bandaraya Kuala Lumpur (DBKL)", "MBPJ": "<PERSON><PERSON>aling Jaya (MBPJ)", "MBSJ": "<PERSON><PERSON>araya <PERSON>ang <PERSON> (MBSJ)", "MBSA": "<PERSON><PERSON> (MBSA)", "MPK": "<PERSON><PERSON> (MPK)", "MBIP": "<PERSON><PERSON> (MBIP)", "MPJB": "<PERSON><PERSON> (MPJBT)", "MBPP": "<PERSON><PERSON> (MBPP)", "MPSP": "<PERSON><PERSON> (MPSP)", "MBMB": "<PERSON><PERSON> (MBMB)", "MPAG": "<PERSON><PERSON> (MPAG)", "TNB": "Tenaga Nasional Berhad (TNB)", "SYABAS": "Syarikat Bekalan Air Selangor (SYABAS)", "IWK": "Indah Water Konsortium (IWK)", "PLUS": "PLUS Malaysia Berhad", "KTMB": "<PERSON><PERSON><PERSON> (KTMB)", "MRT": "Perbadanan Mass Rapid Transit (MRT Corp)", "LRT": "Rapid Rail Sdn Bhd", "HSB": "Hospital Selayang", "HKL": "Hospital Kuala Lumpur", "HUSM": "Hospital Universiti Sains Malaysia (HUSM)", "HUKM": "Hospital Universiti Kebangsaan Malaysia (HUKM)", "UM": "Universiti Malaya (UM)", "UKM": "Universiti Kebangsaan Malaysia (UKM)", "USM": "Universiti Sains Malaysia (USM)", "UTM": "Universiti Teknologi Malaysia (UTM)", "UPM": "Universiti Putra Malaysia (UPM)", "UiTM": "Universiti Teknologi MARA (UiTM)"}, "states": {"JH": "<PERSON><PERSON>", "KD": "Kedah", "KT": "<PERSON><PERSON><PERSON>", "ML": "<PERSON><PERSON>", "NS": "<PERSON><PERSON><PERSON>", "PH": "<PERSON><PERSON>", "PN": "<PERSON><PERSON><PERSON>", "PK": "<PERSON><PERSON>", "PL": "<PERSON><PERSON>", "SB": "Sabah", "SW": "Sarawak", "SL": "Selangor", "TR": "Terengganu", "WP": "W.P. Kuala Lumpur", "LBN": "<PERSON><PERSON><PERSON><PERSON>", "PW": "<PERSON><PERSON><PERSON><PERSON>", "OTH": "Lain-lain"}, "validation": {"required": "Medan ini diperlukan", "email": "<PERSON><PERSON> masukkan alamat emel yang sah", "password": "Kata laluan mestilah sekurang-kurangnya 8 aksara", "passwordMin": "Kata laluan mestilah sekurang-kurangnya 8 aksara", "passwordLowercase": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON><PERSON> satu huruf kecil", "passwordUppercase": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON><PERSON> satu huruf besar", "passwordNumber": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON>nya satu nombor", "passwordSpecial": "<PERSON>a laluan mesti mengandungi sekurang-kurangnya satu aksara khas (@$!%*?&)", "passwordMatch": "<PERSON>a la<PERSON>an tidak sepadan", "nameMin": "Nama mestilah sekurang-kurangnya 2 aksara", "phoneFormat": "Sila masukkan nombor telefon Malaysia yang sah", "roleRequired": "<PERSON>la pilih peranan", "icNumber": "Sila masukkan nombor IC yang sah"}, "errors": {"somethingWrong": "Sesuatu telah berlaku", "tryAgain": "Sila cuba lagi", "networkError": "<PERSON><PERSON>. <PERSON><PERSON> per<PERSON> sambungan anda.", "unauthorized": "Anda tidak dibenarkan untuk melakukan tindakan ini", "notFound": "Sumber yang diminta tidak dijumpai"}, "error": {"title": "Sesu<PERSON>u yang tidak kena", "description": "<PERSON><PERSON> men<PERSON> ralat yang tidak dijangka. Sila cuba lagi atau kembali ke halaman sebelumnya.", "retryButton": "Cuba lagi", "goBackButton": "Kembali"}, "notFound": {"title": "404", "description": "Halaman tidak dijumpai", "backButton": "Ke<PERSON><PERSON> ke Utama"}, "authPortal": {"title": "Selamat Datang ke SimPLE", "subtitle": "<PERSON><PERSON><PERSON> peranan anda untuk mengakses portal yang sesuai", "adminPortalTitle": "Portal Pentadbir", "adminPortalDescription": "Untuk pentadbir sistem dan kakitangan JKR", "contractorPortalTitle": "Portal Kontraktor", "contractorPortalDescription": "Untuk kontraktor berdaftar dan penyedia perkhidmatan", "adminLogin": "Log Masuk <PERSON>bir", "adminRegister": "<PERSON><PERSON><PERSON>", "contractorLogin": "Log Masuk <PERSON>", "contractorRegister": "<PERSON><PERSON>ar Kontraktor", "helpText": "Perlukan bantuan? Hubungi sokongan untuk bantuan.", "heroTitle": "Platform Profesional", "heroSubtitle": "Menghubungkan pentadbir dan kontraktor dengan lancar", "scrollToLearn": "Tatal untuk ketahui lebih lanjut", "adminFeatures": {"projectManagement": "Pengurusan & Pengawasan Projek", "userManagement": "Pengurusan Pengguna & Kawalan Akses", "reports": "Laporan & Papan Pemuka <PERSON>"}, "contractorFeatures": {"bidding": "Pembidaan Projek & Cadangan", "tracking": "Penjejakan Kemajuan & Pelaporan", "compliance": "Pengurusan Dokumen & Pematuhan"}, "aboutSimple": {"title": "Apakah SimPLE?", "subtitle": "Platform Pengurusan Pintar untuk Kecemerlangan Lif", "description": "SimPLE adalah platform digital yang komprehensif yang direka untuk merevolusikan pengurusan lif dan elevator di Malaysia. Kami merapatkan jurang antara pentadbir kerajaan (JKR) dan kontraktor yang be<PERSON>, mewujudkan ekosistem yang lancar untuk penyelen<PERSON>, p<PERSON><PERSON><PERSON>, dan operasi elevator.", "features": {"digital": {"title": "Transformasi Digital", "description": "<PERSON><PERSON><PERSON> da<PERSON> proses berasaskan kertas kepada aliran kerja digital pintar"}, "compliance": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Memastikan semua lif memenuhi piawaian dan peraturan keselamatan Malaysia"}, "efficiency": {"title": "Kecekapan Operasi", "description": "Proses yang diperkemas untuk masa tindak balas yang lebih pantas dan penyam<PERSON>ian perkhidmatan yang lebih baik"}, "transparency": {"title": "Ketelusan <PERSON>uh", "description": "<PERSON><PERSON><PERSON><PERSON> dan pelaporan masa nyata untuk semua pihak berkepentingan"}}}, "nav": {"home": "<PERSON><PERSON>", "about": "Tentang", "whatIsSimple": "Apakah SimPLE?", "contact": "Hubung<PERSON>"}, "services": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Penyelesaian pengurusan lif yang komprehensif untuk bangunan moden", "liftManagement": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pengurusan kitaran hayat lengkap untuk sistem lif", "features": {"maintenance": "Penjadua<PERSON>", "inspection": "Pemeriksaan <PERSON> Berkala", "compliance": "<PERSON><PERSON><PERSON><PERSON>"}}, "safety": {"title": "Keselamatan & Pematuhan", "description": "Memastikan piawaian keselamatan dan pematuhan peraturan", "features": {"standards": "Piawaian Keselamatan Antarabangsa", "certification": "Program Pensijilan Profesional", "reporting": "Pelaporan Keselama<PERSON>rehensif"}}, "platform": {"title": "Platform Digital", "description": "Teknologi canggih untuk operasi yang lancar", "features": {"realtime": "Pemantauan & Amaran <PERSON>", "analytics": "Analitik & Wawasan Data", "integration": "Integrasi Sistem Pihak <PERSON>"}}}, "blog": {"title": "Berita & Wawasan Terkini", "subtitle": "Kekal dikemas kini dengan trend industri dan amalan terbaik", "readMore": "Baca Lagi", "viewAll": "<PERSON><PERSON>", "post1": {"date": "15 Disember 2024", "title": "Amalan Terbaik dalam Penyelenggaraan Lif", "excerpt": "Pelajari tentang strategi penyelenggaraan terkini yang memastikan prestasi dan keselamatan yang optimum."}, "post2": {"date": "10 Disember 2024", "title": "<PERSON><PERSON> Peraturan Keselamatan 2024", "excerpt": "<PERSON><PERSON> kini penting kepada peraturan keselamatan lif dan keperluan pematuhan untuk tahun ini."}, "post3": {"date": "5 Disember 2024", "title": "Transformasi Digital dalam <PERSON> Lif", "excerpt": "Bagaimana teknologi moden merevolusikan cara kita mengurus dan menyelenggara sistem lif."}}, "newsletter": {"title": "Kekal Berhubung", "subtitle": "Dapatkan kemas kini terkini, berita industri, dan wawasan eksk<PERSON><PERSON> terus ke peti mel anda", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat e-mel anda", "subscribe": "<PERSON><PERSON>", "privacy": "<PERSON><PERSON> privasi anda. <PERSON><PERSON> lang<PERSON>an bila-bila masa."}}, "complaints": {"title": "<PERSON>g <PERSON>", "subtitle": "<PERSON><PERSON><PERSON> laporan kerosakan yang telah anda hantar dan <PERSON>nya", "continue": "Teruskan", "stats": {"totalReports": "<PERSON><PERSON><PERSON>", "underReview": "<PERSON><PERSON>", "approved": "Dilulus<PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "table": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> dan jejaki laporan kerosakan anda dengan keupayaan susulan", "reportId": "ID Laporan", "dateSubmitted": "<PERSON><PERSON><PERSON>", "pmaNumber": "Nombor PMA", "location": "<PERSON><PERSON>", "status": "Status", "followUp": "<PERSON><PERSON><PERSON>", "cost": "<PERSON><PERSON> (RM)", "actions": "<PERSON><PERSON><PERSON>", "newReport": "<PERSON><PERSON><PERSON>", "advancedFilter": "<PERSON><PERSON><PERSON>"}, "form": {"title": "<PERSON><PERSON>", "subtitle": "Sistem Pengurusan <PERSON>", "sectionA": {"title": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "complaintDate": "<PERSON><PERSON><PERSON>", "agency": "Agensi", "contractorCompanyName": "<PERSON><PERSON> / Kontraktor", "location": "<PERSON><PERSON>", "pmaNumber": "No PMA Lif", "damageDescription": "<PERSON><PERSON>", "expectedCompletionDate": "<PERSON><PERSON><PERSON>", "involvesManTrap": "Melibatkan Mantrap", "yes": "Ya", "no": "Tidak"}, "sectionB": {"title": "<PERSON><PERSON> Pembetulan", "note": "Bahagian ini boleh diisi selepas pembetulan selesai atau sekarang jika maklumat sudah disediakan", "actualCompletionDate": "<PERSON><PERSON><PERSON>", "completionTime": "<PERSON><PERSON><PERSON>", "damageCause": "<PERSON><PERSON><PERSON>", "repairAction": "<PERSON><PERSON><PERSON>", "proofOfRepair": "<PERSON><PERSON><PERSON>", "beforePhoto": "Gambar <PERSON>", "afterPhoto": "<PERSON><PERSON><PERSON>", "repairCost": "<PERSON><PERSON> (RM)"}, "actions": {"upload": "<PERSON><PERSON>"}, "notes": {"title": "<PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> medan dengan tanda (*) adalah wajib diisi", "proofRequired": "<PERSON><PERSON><PERSON> (gambar) mesti disertakan selepas kerja se<PERSON>ai", "autoSubmit": "<PERSON><PERSON><PERSON> akan dihantar secara automatik"}, "fileUpload": {"acceptedFormats": "Format yang diterima: JPG, PNG, PDF (Maks 10MB setiap fail)", "maxFiles": "Maksimum 5 fail dibenarkan"}, "buttons": {"back": "🔙 Ke<PERSON>li", "submit": "📤 <PERSON><PERSON>", "update": "📤 <PERSON>mas<PERSON>", "submitting": "Menghantar...", "updating": "Mengemaskini..."}, "placeholders": {"email": "<EMAIL>", "selectDate": "<PERSON><PERSON><PERSON> ta<PERSON>h", "selectAgency": "<PERSON><PERSON><PERSON>", "repairCost": "0.00"}}, "status": {"underReview": "<PERSON><PERSON>", "approved": "Dilulus<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "submitted": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "complete": "Lengka<PERSON>"}, "actions": {"upload": "<PERSON><PERSON>", "add": "Tambah", "resubmit": "<PERSON><PERSON>"}, "pagination": {"showing": "<PERSON><PERSON><PERSON><PERSON><PERSON> {start}-{end} daripada {total} entri", "previous": "Sebelumnya", "next": "Seterusnya"}, "upload": {"title": "<PERSON>at <PERSON>", "subtitle": "Tambah dokumen sokongan untuk laporan dalam semakan", "description": "<PERSON><PERSON>, <PERSON><PERSON> tamba<PERSON>, atau dokumen yang diminta", "chooseFiles": "<PERSON><PERSON><PERSON>", "takePhoto": "Ambil Foto"}, "recentActivity": {"title": "Aktiviti Terkini", "subtitle": "<PERSON><PERSON> kini terkini mengenai laporan aduan anda"}}}