'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ComplaintDetails } from '@/features/complaints/components/complaint-details-simple';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import {
  Camera,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Download,
  Edit,
  Eye,
  FileText,
  Filter,
  Plus,
  Upload,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function ComplaintsPage() {
  const [selectedComplaint, setSelectedComplaint] =
    useState<ComplaintUI | null>(null);
  const router = useRouter();

  // Translations
  const t = useTranslations('complaints');

  // Mock data - this would come from your API
  const mockComplaints: ComplaintUI[] = [
    {
      id: '1',
      complaintNumber: 'RPT-2024-001',
      email: '<EMAIL>',
      contactNumber: '+60123456789',
      damageComplaintDate: '2024-03-20',
      expectedCompletionDate: '2024-04-20',
      agency: 'KLCC Twin Towers',
      contractorCompanyName: 'ABC Construction',
      location: 'KLCC Twin Towers',
      noPmaLif: 'PMA-001-KLCC-A',
      status: 'in_progress',
      description: 'Under Review',
      createdAt: '2024-03-20',
      proofOfRepairFiles: [],
    },
    {
      id: '2',
      complaintNumber: 'RPT-2024-002',
      email: '<EMAIL>',
      contactNumber: '+60123456789',
      damageComplaintDate: '2024-03-18',
      expectedCompletionDate: '2024-04-18',
      actualCompletionDate: '2024-03-25',
      agency: 'KLCC Twin Towers',
      contractorCompanyName: 'ABC Construction',
      location: 'KLCC Twin Towers',
      noPmaLif: 'PMA-002-KLCC-B',
      status: 'completed',
      description: 'Approved',
      createdAt: '2024-03-18',
      proofOfRepairFiles: [{ name: '2 docs uploaded', url: '#', size: 1024 }],
    },
    {
      id: '3',
      complaintNumber: 'RPT-2024-003',
      email: '<EMAIL>',
      contactNumber: '+60123456789',
      damageComplaintDate: '2024-03-15',
      expectedCompletionDate: '2024-04-15',
      agency: 'Petaling Jaya Office',
      contractorCompanyName: 'DEF Construction',
      location: 'Petaling Jaya Office',
      noPmaLif: 'PMA-003-PJ-A',
      status: 'cancelled',
      description: 'Rejected',
      createdAt: '2024-03-15',
      proofOfRepairFiles: [],
    },
    {
      id: '4',
      complaintNumber: 'RPT-2024-004',
      email: '<EMAIL>',
      contactNumber: '+60123456789',
      damageComplaintDate: '2024-03-12',
      expectedCompletionDate: '2024-04-12',
      agency: 'KLCC Twin Towers',
      contractorCompanyName: 'ABC Construction',
      location: 'KLCC Twin Towers',
      noPmaLif: 'PMA-001-KLCC-A',
      status: 'completed',
      description: 'Completed',
      createdAt: '2024-03-12',
      proofOfRepairFiles: [],
    },
    {
      id: '5',
      complaintNumber: 'RPT-2024-005',
      email: '<EMAIL>',
      contactNumber: '+60123456789',
      damageComplaintDate: '2024-03-10',
      expectedCompletionDate: '2024-04-10',
      agency: 'Shah Alam Complex',
      contractorCompanyName: 'GHI Construction',
      location: 'Shah Alam Complex',
      noPmaLif: 'PMA-004-SHA-A',
      status: 'pending',
      description: 'Submitted',
      createdAt: '2024-03-10',
      proofOfRepairFiles: [],
    },
  ];

  const recentActivity = [
    {
      id: '1',
      type: 'status_update',
      description: 'RPT-2024-001 status updated',
      details:
        'Status changed to "Under Review" - Additional documents requested',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      type: 'approval',
      description: 'RPT-2024-002 approved',
      details: 'Repair work authorized - Estimated completion: 2024-03-25',
      timestamp: '1 day ago',
    },
    {
      id: '3',
      type: 'resubmit',
      description: 'RPT-2024-003 requires revision',
      details: 'Cost estimation needs adjustment - Please resubmit',
      timestamp: '3 days ago',
    },
  ];

  // Calculate stats
  const totalReports = mockComplaints.length;
  const underReview = mockComplaints.filter(
    (c) => c.status === 'in_progress',
  ).length;
  const approved = mockComplaints.filter(
    (c) => c.status === 'completed',
  ).length;
  const rejected = mockComplaints.filter(
    (c) => c.status === 'cancelled',
  ).length;
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'in_progress':
        return (
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {t('status.underReview')}
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {t('status.approved')}
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            {t('status.rejected')}
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {t('status.submitted')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getCostForComplaint = (complaintNumber: string) => {
    const costs: Record<string, number> = {
      'RPT-2024-001': 2500,
      'RPT-2024-002': 1200,
      'RPT-2024-003': 800,
      'RPT-2024-004': 3500,
      'RPT-2024-005': 1800,
    };
    return costs[complaintNumber] || 0;
  };

  const handleViewComplaint = (complaint: ComplaintUI) => {
    setSelectedComplaint(complaint);
  };
  const handleEditComplaint = () => {
    if (selectedComplaint) {
      router.push(`/complaints/${selectedComplaint.id}/edit`);
    }
  };

  const handleEditComplaintFromTable = (complaint: ComplaintUI) => {
    router.push(`/complaints/${complaint.id}/edit`);
  };

  const handleCloseDetails = () => {
    setSelectedComplaint(null);
  };

  const handleCreateNewComplaint = () => {
    router.push('/complaints/new');
  };
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
            <p className="text-gray-600 text-sm">{t('subtitle')}</p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-blue-700">
                  {totalReports}
                </p>
                <p className="text-sm text-blue-600">
                  {t('stats.totalReports')}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-orange-700">
                  {underReview}
                </p>
                <p className="text-sm text-orange-600">
                  {t('stats.underReview')}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-green-700">{approved}</p>
                <p className="text-sm text-green-600">{t('stats.approved')}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-red-700">{rejected}</p>
                <p className="text-sm text-red-600">{t('stats.rejected')}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Complaint Reports Table */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold">
                  {t('table.title')}
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    {t('table.advancedFilter')}
                  </Button>{' '}
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700"
                    onClick={handleCreateNewComplaint}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('table.newReport')}
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-600">{t('table.subtitle')}</p>
            </CardHeader>

            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-medium">
                      {t('table.reportId')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.dateSubmitted')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.pmaNumber')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.location')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.status')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.followUp')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.cost')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.actions')}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockComplaints.map((complaint) => (
                    <TableRow key={complaint.id} className="hover:bg-gray-50">
                      <TableCell>
                        <Button
                          variant="link"
                          className="p-0 h-auto text-blue-600 hover:text-blue-800"
                          onClick={() => handleViewComplaint(complaint)}
                        >
                          {complaint.complaintNumber}
                        </Button>
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.damageComplaintDate}
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.noPmaLif}
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.location}
                      </TableCell>
                      <TableCell>{getStatusBadge(complaint.status)}</TableCell>
                      <TableCell>
                        {' '}
                        {complaint.proofOfRepairFiles &&
                        complaint.proofOfRepairFiles.length > 0 ? (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 h-6"
                            >
                              {t('actions.upload')}
                            </Button>
                            <span className="text-xs text-gray-600 self-center">
                              {complaint.proofOfRepairFiles[0].name}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs px-2 py-1 h-6"
                            >
                              + {t('actions.add')}
                            </Button>
                          </div>
                        ) : complaint.status === 'completed' ? (
                          <span className="text-xs text-green-600">
                            ✓ {t('status.complete')}
                          </span>
                        ) : complaint.status === 'cancelled' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-red-50 text-red-600 text-xs px-2 py-1 h-6 border-red-200"
                          >
                            {t('actions.resubmit')}
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 h-6"
                          >
                            {t('actions.upload')}
                          </Button>
                        )}
                      </TableCell>
                      <TableCell className="text-sm">
                        {getCostForComplaint(
                          complaint.complaintNumber,
                        ).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleViewComplaint(complaint)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => {
                              /* Download functionality */
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() =>
                              handleEditComplaintFromTable(complaint)
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {/* Pagination */}
              <div className="flex justify-between items-center p-4 border-t">
                <span className="text-sm text-gray-600">
                  {t('pagination.showing', {
                    start: '1',
                    end: '5',
                    total: '12',
                  })}
                </span>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4" />
                    {t('pagination.previous')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-600 text-white"
                  >
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="sm">
                    {t('pagination.next')}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Upload Documents Section */}
        <Card>
          <CardHeader className="pb-4 ">
            <CardTitle className="text-lg font-semibold">
              {t('upload.title')}
            </CardTitle>
            <p className="text-sm text-gray-600">{t('upload.subtitle')}</p>
          </CardHeader>
          <CardContent>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-sm font-medium text-gray-900 mb-2">
                {t('upload.title')}
              </p>
              <p className="text-xs text-gray-500 mb-4">
                {t('upload.description')}
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-blue-600 text-white border-blue-600"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {t('upload.chooseFiles')}
                </Button>
                <Button variant="outline" size="sm">
                  <Camera className="h-4 w-4 mr-2" />
                  {t('upload.takePhoto')}
                </Button>
              </div>
            </div>

            {/* Uploaded Files */}
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">progress_photo_001.jpg</span>
                <span className="text-xs text-gray-500">2024-03-21 10:30</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-500"
                >
                  ×
                </Button>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">additional_report.pdf</span>
                <span className="text-xs text-gray-500">2024-03-21 11:15</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-500"
                >
                  ×
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold">
              {t('recentActivity.title')}
            </CardTitle>
            <p className="text-sm text-gray-600">
              {t('recentActivity.subtitle')}
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex gap-3">
                <div className="flex-shrink-0">
                  {activity.type === 'status_update' && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  )}
                  {activity.type === 'approval' && (
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  )}
                  {activity.type === 'resubmit' && (
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-600">{activity.details}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {activity.timestamp}
                  </p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Complaint Details Modal */}
      {selectedComplaint && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <ComplaintDetails
              complaint={selectedComplaint}
              onClose={handleCloseDetails}
              onEdit={handleEditComplaint}
            />
          </div>
        </div>
      )}
    </div>
  );
}
